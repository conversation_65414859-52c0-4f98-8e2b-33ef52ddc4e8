<template>
  <div class="rule-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="page-title" data-testid="dashboard-title">模板状态仪表盘</h1>
        <p class="page-description">管理和监控所有规则模板的状态，支持模板下载和数据上传</p>
      </div>
      <div class="header-actions">
        <el-button :icon="Refresh" circle @click="handleRefresh" :loading="isLoading" />
      </div>
    </div>

    <!-- 统计卡片 - 五个统计量 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="4" v-for="stat in statsConfig" :key="stat.key">
          <el-card class="stat-card" shadow="never">
            <div class="stat-label">
              <el-icon :size="16" :color="stat.color">
                <component :is="stat.icon" />
              </el-icon>
              <span class="stat-text">{{ stat.label }}</span>
            </div>
            <div class="stat-number" :style="{ color: stat.color }">
              {{ (statusCounts && statusCounts[stat.key]) || 0 }}
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-row :gutter="16" align="middle">
        <el-col :lg="10" :md="24">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索规则名称或规则Key..."
            :prefix-icon="Search"
            clearable
            data-testid="search-input"
            @input="handleSearch"
          />
        </el-col>
        <el-col :lg="6" :md="12">
          <el-select
            v-model="selectedStatus"
            placeholder="筛选状态"
            clearable
            data-testid="status-filter"
            @change="handleStatusFilter"
          >
            <el-option label="全部状态" value="" />
            <el-option
              v-for="status in (availableStatuses || [])"
              :key="status"
              :label="formatStatus(status)"
              :value="status"
            />
          </el-select>
        </el-col>
        <el-col :lg="8" :md="12" class="view-switch-col">
          <el-switch
            :model-value="isCardView"
            active-text="卡片视图"
            inactive-text="列表视图"
            inline-prompt
            :active-icon="Grid"
            :inactive-icon="Tickets"
            @change="handleViewToggle"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 错误状态显示 -->
    <div v-if="hasError" class="error-section">
      <el-alert
        :title="currentError?.message || '系统错误'"
        type="error"
        :description="currentError?.details?.message"
        show-icon
        :closable="true"
        @close="appStore.clearError()"
      />
    </div>

    <!-- 数据展示区域 -->
    <LoadingWrapper :loading="isLoading" min-height="400px">
      <!-- 卡片视图 -->
      <div v-if="isCardView" :style="{
        marginTop: '8px'
      }">
        <div :style="{
          display: 'grid',
          gap: '20px',
          gridTemplateColumns: 'repeat(3, 1fr)'
        }" class="responsive-grid" data-testid="rules-grid">
          <RuleStatusCard
            v-for="rule in finalFilteredRules"
            :key="rule.rule_key"
            :rule="rule"
            @view-detail="handleViewDetail"
            @download-template="handleDownloadTemplate"
            @upload-data="handleUploadData"
          />
        </div>
        <el-empty v-if="(finalFilteredRules || []).length === 0" description="暂无规则数据" />
      </div>

      <!-- 表格视图 -->
      <DataTable
        v-else
        :data="finalFilteredRules"
        :loading="false"
        :searchable="false"
        :show-pagination="true"
        :total="(finalFilteredRules || []).length"
        empty-text="暂无规则数据"
      >
        <el-table-column prop="rule_name" label="规则名称" min-width="300" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="150" align="center">
          <template #default="{ row }">
            <StatusTag :status="row.status" />
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="220" align="center">
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                size="small"
                :icon="View"
                :disabled="row.status === 'DEPRECATED'"
                @click="handleViewDetail(row)"
                :title="row.status === 'DEPRECATED' ? '已弃用的规则模板无法查看详情' : '查看规则模板详情'"
              >
                详情
              </el-button>
              <el-button
                size="small"
                :icon="Download"
                :disabled="row.status === 'DEPRECATED'"
                @click="handleDownloadTemplate(row)"
                :title="row.status === 'DEPRECATED' ? '已弃用的规则模板无法下载' : '下载模板'">
                模板
              </el-button>
              <el-button
                type="primary"
                size="small"
                :icon="Upload"
                :disabled="row.status === 'DEPRECATED'"
                @click="handleUploadData(row)"
                :title="row.status === 'DEPRECATED' ? '已弃用的规则模板无法上传数据' : '上传数据'">
                上传
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </DataTable>
    </LoadingWrapper>

    <!-- 规则详情抽屉 -->
    <RuleDetailDrawer
      v-model="detailDrawerVisible"
      :rule-key="selectedRuleKey"
      @download-template="handleDownloadTemplate"
      @upload-data="handleUploadData"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  Search, Refresh, View, Download, Upload, Grid, Tickets,
  DataAnalysis, StarFilled, EditPen, CircleCheck, Delete
} from '@element-plus/icons-vue'
import { formatDate } from '../utils/dateUtils'
import LoadingWrapper from '../components/common/LoadingWrapper.vue'
import DataTable from '../components/common/DataTable.vue'
import StatusTag from '../components/common/StatusTag.vue'
import RuleStatusCard from '../components/business/RuleStatusCard.vue'
import RuleDetailDrawer from '../components/business/RuleDetailDrawer.vue'

// Composables
import { useRuleManagement } from '../composables/business/useRuleManagement'
import { useSearch } from '../composables/ui/useSearch'
import { useViewToggle } from '../composables/ui/useViewToggle'
import { useAppStore } from '../stores/app'
import { storeToRefs } from 'pinia'

// 全局状态管理
const appStore = useAppStore()
const { globalLoading, hasError, currentError } = storeToRefs(appStore)

// 使用规则管理功能
const {
  rules,
  loading,
  statusCounts,
  selectedRuleKey,
  detailDrawerVisible,
  availableStatuses,
  formatStatus,
  handleRefresh,
  handleViewDetail,
  handleCloseDetail,
  handleDownloadTemplate,
  handleUploadData
} = useRuleManagement()

// 统一加载状态
const isLoading = computed(() => loading.value)

// 使用搜索功能
const { searchKeyword, searchResults: filteredRules } = useSearch(
  rules,
  ['rule_name', 'rule_key'],
  { caseSensitive: false }
)

// 使用视图切换功能
const { currentView, isCardView, isTableView, switchView } = useViewToggle('card', ['card', 'table'])

// 状态筛选
const selectedStatus = ref('')

// 应用状态筛选到搜索结果
const finalFilteredRules = computed(() => {
  let result = filteredRules.value || []

  // 按状态筛选
  if (selectedStatus.value && Array.isArray(result)) {
    result = result.filter(rule => rule?.status === selectedStatus.value)
  }

  return result || []
})

// 搜索和筛选处理
const handleSearch = () => {
  // 搜索逻辑已在useSearch Composable中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

// 统计卡片配置
const statsConfig = [
  {
    key: 'TOTAL',
    label: '总计',
    icon: DataAnalysis,
    color: '#409eff'
  },
  {
    key: 'NEW',
    label: '新增',
    icon: StarFilled,
    color: '#67c23a'
  },
  {
    key: 'CHANGED',
    label: '逻辑变更',
    icon: EditPen,
    color: '#e6a23c'
  },
  {
    key: 'READY',
    label: '就绪',
    icon: CircleCheck,
    color: '#67c23a'
  },
  {
    key: 'DEPRECATED',
    label: '已废弃',
    icon: Delete,
    color: '#f56c6c'
  }
]

// 视图切换处理
const handleViewToggle = (isCard) => {
  switchView(isCard ? 'card' : 'table')
}

// 生命周期
onMounted(async () => {
  await handleRefresh()
})
</script>

<style scoped>
.rule-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #1d2129;
}

.page-description {
  margin-top: 4px;
  color: #86909c;
  font-size: 14px;
}

.header-actions .el-button.is-circle {
  width: 40px;
  height: 40px;
  font-size: 18px;
}

.stats-cards .stat-card {
  border: 1px solid #e5e6e8;
  border-radius: 8px;
  background-color: #fff;
  transition: all 0.2s ease-in-out;
}
.stats-cards .stat-card:hover {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}
.stat-card .el-card__body {
  padding: 20px !important;
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4e5969;
  margin-bottom: 12px;
}

.stat-text {
  font-weight: 500;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
  transition: color 0.2s ease;
}

.filter-section {
  background-color: #ffffff;
  padding: 16px 20px;
  border-radius: 8px;
  border: 1px solid #e5e6e8;
}

.view-switch-col {
  display: flex;
  justify-content: flex-end;
}

/* 响应式网格布局 - 使用内联样式控制，这里保留作为备用 */

.rule-dashboard :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e6e8;
}
.rule-dashboard :deep(.el-table th.el-table__cell) {
  background-color: #f7f8fa;
  color: #4e5969;
}
.rule-dashboard :deep(.el-table .el-button-group .el-button) {
  margin: 0 2px;
}

@media (max-width: 992px) {
  .filter-section .el-row {
    gap: 12px;
  }
  .filter-section .el-col {
    width: 100% !important;
  }
  .view-switch-col {
    justify-content: flex-start;
  }
}

/* 小屏幕优化 */
@media (max-width: 768px) {
  .rule-dashboard {
    gap: 16px;
  }
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  .header-actions {
    width: 100%;
    text-align: right;
  }
  .stats-cards .el-col {
    width: 50%;
    margin-bottom: 12px;
  }
}
</style>